import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:web_app/dartMain/dartMain_screens/questionnaire_screen.dart';
import 'package:web_app/main.dart';
import 'package:web_app/widgets/universal_widgets.dart';
import 'package:web_app/database/database_logic/write_database_metadata.dart';
import 'package:web_app/database/database_logic/database_logic_fetch/fetch_database.dart';
import 'package:image_picker/image_picker.dart';
import 'package:web_app/database/database_logic/upload_data.dart';
import 'package:web_app/dartMain/dartMain_logic/project_settings_logic.dart';
import 'package:web_app/dartMain/dartMain_logic/project_delete_logic.dart';
import 'package:web_app/dartMain/dartMain_logic/dartMain_logic_fetch/fetch_project_data.dart';
import 'package:cloud_functions/cloud_functions.dart';
import 'package:permission_handler/permission_handler.dart';

class ProjectSettingsWidget extends ConsumerStatefulWidget {
  const ProjectSettingsWidget({super.key});

  @override
  ConsumerState<ProjectSettingsWidget> createState() =>
      _ProjectSettingsWidgetState();
}

class _ProjectSettingsWidgetState extends ConsumerState<ProjectSettingsWidget> {
  UniversalWidgets universals = UniversalWidgets();
  WriteDatabase writeDatabase = WriteDatabase();
  FetchDatabase fetch = FetchDatabase();
  UploadData uploadData = UploadData();
  ProjectSettingsLogic logic = ProjectSettingsLogic();
  ProjectsDeleteLogic deleteLogic = ProjectsDeleteLogic();
  FetchProjectData fetchProjectData = FetchProjectData();
  final TextEditingController _tagnameController = TextEditingController();

  bool isPublic = true; // Default to public
  final TextEditingController _projectDescriptionController =
      TextEditingController();
  final ImagePicker _picker = ImagePicker();

  ValueNotifier<bool> isLoading =
      ValueNotifier(false); // Loading state notifier

  bool _isInitialized = false;
  bool _isLoading = true;
  int _subscribersCount = 0;
  double _storageUsed = 0.0;

  @override
  void initState() {
    super.initState();
    // Initialize data when widget is first created
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeProjectData();
    });
  }

  Future<void> _initializeProjectData() async {
    if (_isInitialized) return;

    try {
      final projectId = ref.read(currentProjectID);
      print("Initializing project data for ID: $projectId");

      // Fetch all project data concurrently
      final results = await Future.wait([
        fetch.fetchProjectDescription(projectId),
        fetch.fetchProjectThumbnailName(projectId),
        fetch.fetchProjectThumbnailUrl(projectId),
        fetchProjectData.fetchNumberOfSubscribers(ref),
        fetchProjectData.fetchStorageUsedByProject(ref),
      ]);

      print("Fetched results: $results");

      if (mounted) {
        // Set project description
        _projectDescriptionController.text = results[0] as String;
        ref.read(projectDescriptionProvider.notifier).state =
            results[0] as String;

        // Set thumbnail data
        ref.read(currentProjectThumbnailName.notifier).state =
            results[1] as String;
        ref.read(currentProjectThumbnailUrlProvider.notifier).state =
            results[2] as String;

        // Store subscribers and storage data for use in build method
        _subscribersCount = results[3] as int;
        _storageUsed = results[4] as double;

        print(
            "Set _subscribersCount: $_subscribersCount, _storageUsed: $_storageUsed");

        // Set loading to false and trigger rebuild
        setState(() {
          _isLoading = false;
        });
      }

      _isInitialized = true;
    } catch (e) {
      print('Error initializing project data: $e');
      // Set loading to false even on error to show the UI
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  String _formatStorageSize(double storageGB) {
    if (storageGB >= 1.0) {
      return '${storageGB.toStringAsFixed(2)} GB';
    } else if (storageGB >= 0.001) {
      double storageMB = storageGB * 1024;
      return '${storageMB.toStringAsFixed(2)} MB';
    } else {
      double storageKB = storageGB * 1024 * 1024;
      return '${storageKB.toStringAsFixed(2)} KB';
    }
  }

  @override
  void dispose() {
    _projectDescriptionController.dispose();
    _tagnameController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    String selectedFileName = ref.watch(currentProjectThumbnailName);
    var projectId = ref.watch(currentProjectID);
    var visibleProjectId = ref.watch(currentProjectVisibleIDProvider);
    AsyncValue<List<String>> tagsAsyncValue =
        ref.watch(fetchProjectTagsProvider);
    final projectName = ref.watch(currentProjectName);

    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        elevation: 0,
        backgroundColor: Colors.white,
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(1.0),
          child: Container(
            height: 2.0,
            color: const Color.fromARGB(255, 44, 148, 44),
          ),
        ),
        title: const Text(
          'Project Settings',
          style: TextStyle(
            color: Color.fromARGB(255, 44, 148, 44),
            fontWeight: FontWeight.w600,
            fontSize: 20,
            letterSpacing: 0.5,
          ),
        ),
        iconTheme: const IconThemeData(color: Color.fromARGB(255, 44, 148, 44)),
      ),
      body: _isLoading
          ? const Center(
              child: CircularProgressIndicator(
                  color: Color.fromARGB(255, 44, 148, 44)))
          : tagsAsyncValue.when(
              loading: () => const Center(
                  child: CircularProgressIndicator(
                      color: Color.fromARGB(255, 44, 148, 44))),
              error: (error, _) =>
                  Center(child: Text('Error fetching tags: $error')),
              data: (tags) {
                // Use FutureProvider for project visibility only
                return ref
                    .watch(projectVisibilityFutureProvider(projectId))
                    .when(
                      loading: () => const Center(
                          child: CircularProgressIndicator(
                              color: Color.fromARGB(255, 44, 148, 44))),
                      error: (error, _) => Center(child: Text('Error: $error')),
                      data: (projectVisibility) {
                        String numSubscribers = _subscribersCount.toString();
                        bool isPublic = projectVisibility == "public";
                        double storageUsed = _storageUsed;

                        return SafeArea(
                          child: SingleChildScrollView(
                            child: Padding(
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 16.0),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  const SizedBox(height: 16),

                                  // Project Information Section
                                  _buildSectionHeader('Project Information'),
                                  _buildCard(
                                    context,
                                    Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        _buildInfoRow(
                                          context,
                                          'Project Name',
                                          projectName,
                                          Icons.title_outlined,
                                        ),
                                        const Divider(height: 24),
                                        _buildInfoRow(
                                          context,
                                          'Project ID',
                                          visibleProjectId,
                                          Icons.tag_outlined,
                                        ),
                                        const Divider(height: 24),
                                        _buildInfoRow(
                                          context,
                                          'Visibility',
                                          projectVisibility,
                                          Icons.visibility_outlined,
                                          isHighlighted:
                                              projectVisibility == 'public',
                                        ),
                                        const Divider(height: 24),
                                        _buildInfoRow(
                                          context,
                                          'Subscribers',
                                          numSubscribers,
                                          Icons.people_outline,
                                        ),
                                        const Divider(height: 24),
                                        _buildInfoRow(
                                          context,
                                          'Storage Used',
                                          _formatStorageSize(storageUsed),
                                          Icons.storage_outlined,
                                        ),
                                      ],
                                    ),
                                  ),

                                  const SizedBox(height: 24),

                                  // Project Description Section
                                  _buildSectionHeader('Project Description'),
                                  _buildCard(
                                    context,
                                    Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        TextField(
                                          controller:
                                              _projectDescriptionController,
                                          decoration: InputDecoration(
                                            hintText:
                                                'Enter project description',
                                            contentPadding:
                                                const EdgeInsets.symmetric(
                                                    horizontal: 16,
                                                    vertical: 12),
                                            border: OutlineInputBorder(
                                              borderRadius:
                                                  BorderRadius.circular(8),
                                              borderSide: BorderSide(
                                                  color: Colors.grey.shade300),
                                            ),
                                            enabledBorder: OutlineInputBorder(
                                              borderRadius:
                                                  BorderRadius.circular(8),
                                              borderSide: BorderSide(
                                                  color: Colors.grey.shade300),
                                            ),
                                            focusedBorder: OutlineInputBorder(
                                              borderRadius:
                                                  BorderRadius.circular(8),
                                              borderSide: const BorderSide(
                                                  color: Color.fromARGB(
                                                      255, 44, 148, 44)),
                                            ),
                                          ),
                                          style: const TextStyle(fontSize: 16),
                                          minLines: 5,
                                          maxLines: null,
                                        ),
                                        const SizedBox(height: 16),
                                        SizedBox(
                                          width: double.infinity,
                                          child: ElevatedButton(
                                            onPressed: () {
                                              try {
                                                writeDatabase
                                                    .updateProjectDescription(
                                                  _projectDescriptionController
                                                      .text,
                                                  ref,
                                                );
                                                ref
                                                        .read(
                                                            projectDescriptionProvider
                                                                .notifier)
                                                        .state =
                                                    _projectDescriptionController
                                                        .text;

                                                ScaffoldMessenger.of(context)
                                                    .showSnackBar(
                                                  const SnackBar(
                                                    content: Text(
                                                        'Description updated successfully.'),
                                                    backgroundColor:
                                                        Color.fromARGB(
                                                            255, 44, 148, 44),
                                                  ),
                                                );
                                              } catch (e) {
                                                ScaffoldMessenger.of(context)
                                                    .showSnackBar(
                                                  SnackBar(
                                                    content: Text(
                                                        'Error updating description: $e'),
                                                    backgroundColor: Colors.red,
                                                  ),
                                                );
                                              }
                                            },
                                            style: ElevatedButton.styleFrom(
                                              backgroundColor:
                                                  const Color.fromARGB(
                                                      255, 44, 148, 44),
                                              foregroundColor: Colors.white,
                                              elevation: 0,
                                              padding:
                                                  const EdgeInsets.symmetric(
                                                      vertical: 12),
                                              shape: RoundedRectangleBorder(
                                                borderRadius:
                                                    BorderRadius.circular(8),
                                              ),
                                            ),
                                            child: const Text(
                                              'Update Description',
                                              style: TextStyle(
                                                  fontSize: 16,
                                                  fontWeight: FontWeight.w500),
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),

                                  const SizedBox(height: 24),

                                  // Thumbnail Section
                                  _buildSectionHeader('Project Thumbnail'),
                                  _buildCard(
                                    context,
                                    Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Row(
                                          children: [
                                            Container(
                                              width: 60,
                                              height: 60,
                                              decoration: BoxDecoration(
                                                color: const Color.fromARGB(
                                                    25, 44, 148, 44),
                                                borderRadius:
                                                    BorderRadius.circular(8),
                                              ),
                                              child: const Icon(
                                                Icons.image_outlined,
                                                color: Color.fromARGB(
                                                    255, 44, 148, 44),
                                                size: 30,
                                              ),
                                            ),
                                            const SizedBox(width: 16),
                                            Expanded(
                                              child: Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  const Text(
                                                    'Current Thumbnail',
                                                    style: TextStyle(
                                                      fontSize: 14,
                                                      fontWeight:
                                                          FontWeight.w600,
                                                      color: Colors.grey,
                                                    ),
                                                  ),
                                                  const SizedBox(height: 4),
                                                  Text(
                                                    selectedFileName.isEmpty
                                                        ? 'No thumbnail selected'
                                                        : selectedFileName,
                                                    style: const TextStyle(
                                                      fontSize: 16,
                                                      color: Colors.black87,
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ],
                                        ),
                                        const SizedBox(height: 16),
                                        Row(
                                          children: [
                                            Expanded(
                                              child: ElevatedButton.icon(
                                                icon: const Icon(
                                                    Icons.photo_library),
                                                label:
                                                    const Text('From Gallery'),
                                                onPressed: () async {
                                                  await _pickAndUploadImage(
                                                      context);
                                                },
                                                style: ElevatedButton.styleFrom(
                                                  backgroundColor:
                                                      const Color.fromARGB(
                                                          255, 44, 148, 44),
                                                  foregroundColor: Colors.white,
                                                  elevation: 0,
                                                  padding: const EdgeInsets
                                                      .symmetric(vertical: 12),
                                                  shape: RoundedRectangleBorder(
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            8),
                                                  ),
                                                ),
                                              ),
                                            ),
                                            const SizedBox(width: 8),
                                            Expanded(
                                              child: ElevatedButton.icon(
                                                icon: const Icon(
                                                    Icons.camera_alt),
                                                label: const Text('Take Photo'),
                                                onPressed: () async {
                                                  await _pickAndUploadCameraImage(
                                                      context);
                                                },
                                                style: ElevatedButton.styleFrom(
                                                  backgroundColor:
                                                      const Color.fromARGB(
                                                          255, 44, 148, 44),
                                                  foregroundColor: Colors.white,
                                                  elevation: 0,
                                                  padding: const EdgeInsets
                                                      .symmetric(vertical: 12),
                                                  shape: RoundedRectangleBorder(
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            8),
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ],
                                    ),
                                  ),

                                  const SizedBox(height: 24),

                                  // Tags Section
                                  _buildSectionHeader('Project Tags'),
                                  _buildCard(
                                    context,
                                    Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        const Text(
                                          'Add tags to help users find your project',
                                          style: TextStyle(
                                            fontSize: 14,
                                            color: Colors.black87,
                                          ),
                                        ),
                                        const SizedBox(height: 16),
                                        Wrap(
                                          spacing: 8.0,
                                          runSpacing: 8.0,
                                          children: [
                                            ...tags.map((tag) => Chip(
                                                  label: Text(tag),
                                                  backgroundColor:
                                                      const Color.fromARGB(
                                                          25, 44, 148, 44),
                                                  labelStyle: const TextStyle(
                                                      color: Color.fromARGB(
                                                          255, 44, 148, 44)),
                                                  deleteIconColor:
                                                      const Color.fromARGB(
                                                          255, 44, 148, 44),
                                                  onDeleted: () {
                                                    logic
                                                        .removeProjectTagToFirestore(
                                                            ref, tag);
                                                    ref.invalidate(
                                                        fetchProjectTagsProvider);
                                                  },
                                                  shape: RoundedRectangleBorder(
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            16),
                                                    side: const BorderSide(
                                                      color: Color.fromARGB(
                                                          76, 44, 148, 44),
                                                      width: 1,
                                                    ),
                                                  ),
                                                )),
                                            ActionChip(
                                              avatar: const Icon(
                                                Icons.add,
                                                size: 18,
                                                color: Color.fromARGB(
                                                    255, 44, 148, 44),
                                              ),
                                              label: const Text('Add Tag'),
                                              backgroundColor: Colors.white,
                                              labelStyle: const TextStyle(
                                                  color: Color.fromARGB(
                                                      255, 44, 148, 44)),
                                              shape: RoundedRectangleBorder(
                                                borderRadius:
                                                    BorderRadius.circular(16),
                                                side: const BorderSide(
                                                  color: Color.fromARGB(
                                                      76, 44, 148, 44),
                                                  width: 1,
                                                ),
                                              ),
                                              onPressed: () {
                                                universals
                                                    .showCustomPopupWithTextfield(
                                                        context,
                                                        "Add a Tag",
                                                        "Write your tag name here",
                                                        _tagnameController,
                                                        () async {
                                                  await logic
                                                      .uploadProjectTagToFirestore(
                                                          ref,
                                                          _tagnameController
                                                              .text);
                                                  _tagnameController.clear();
                                                  ref.invalidate(
                                                      fetchProjectTagsProvider);
                                                });
                                              },
                                            ),
                                          ],
                                        ),
                                      ],
                                    ),
                                  ),

                                  const SizedBox(height: 24),

                                  // Questionnaire Section (for private projects)
                                  if (!isPublic) ...[
                                    _buildSectionHeader(
                                        'Application Questionnaire'),
                                    _buildCard(
                                      context,
                                      Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          const Text(
                                            'Manage the application process for users who want to join your private project.',
                                            style: TextStyle(
                                              fontSize: 14,
                                              color: Colors.black87,
                                            ),
                                          ),
                                          const SizedBox(height: 16),
                                          SizedBox(
                                            width: double.infinity,
                                            child: ElevatedButton.icon(
                                              icon: const Icon(
                                                  Icons.edit_document),
                                              label: const Text(
                                                  'Edit Questionnaire'),
                                              onPressed: () => Navigator.push(
                                                context,
                                                MaterialPageRoute(
                                                    builder: (context) =>
                                                        QuestionnaireScreen()),
                                              ),
                                              style: ElevatedButton.styleFrom(
                                                backgroundColor:
                                                    const Color.fromARGB(
                                                        255, 44, 148, 44),
                                                foregroundColor: Colors.white,
                                                elevation: 0,
                                                padding:
                                                    const EdgeInsets.symmetric(
                                                        vertical: 12),
                                                shape: RoundedRectangleBorder(
                                                  borderRadius:
                                                      BorderRadius.circular(8),
                                                ),
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    const SizedBox(height: 24),
                                  ],

                                  // Danger Zone
                                  _buildSectionHeader('Danger Zone',
                                      isWarning: true),
                                  _buildDangerCard(
                                    context,
                                    Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        const Text(
                                          'Delete this project',
                                          style: TextStyle(
                                            fontSize: 16,
                                            fontWeight: FontWeight.bold,
                                            color: Colors.red,
                                          ),
                                        ),
                                        const SizedBox(height: 8),
                                        const Text(
                                          'This action cannot be undone. All project data, including files, discussions, and subscriber information will be permanently deleted.',
                                          style: TextStyle(
                                            fontSize: 14,
                                            color: Colors.black87,
                                          ),
                                        ),
                                        const SizedBox(height: 16),
                                        SizedBox(
                                          width: double.infinity,
                                          child: ElevatedButton.icon(
                                            icon: const Icon(
                                                Icons.delete_forever),
                                            label: const Text('Delete Project'),
                                            onPressed: () =>
                                                showWarningDialog(context, ref),
                                            style: ElevatedButton.styleFrom(
                                              backgroundColor:
                                                  Colors.red.shade50,
                                              foregroundColor:
                                                  Colors.red.shade900,
                                              elevation: 0,
                                              padding:
                                                  const EdgeInsets.symmetric(
                                                      vertical: 12),
                                              shape: RoundedRectangleBorder(
                                                borderRadius:
                                                    BorderRadius.circular(8),
                                                side: BorderSide(
                                                    color: Colors.red.shade300),
                                              ),
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),

                                  const SizedBox(height: 40),
                                ],
                              ),
                            ),
                          ),
                        );
                      },
                    );
              },
            ),
    );
  }

  // Section header
  Widget _buildSectionHeader(String title, {bool isWarning = false}) {
    return Padding(
      padding: const EdgeInsets.only(left: 8.0, bottom: 8.0),
      child: Text(
        title,
        style: TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w600,
          letterSpacing: 0.5,
          color: isWarning ? Colors.red.shade700 : Colors.grey.shade700,
        ),
      ),
    );
  }

  // Card container
  Widget _buildCard(BuildContext context, Widget child) {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.only(bottom: 8.0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(10),
            blurRadius: 6,
            spreadRadius: 0,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: child,
      ),
    );
  }

  // Danger zone card
  Widget _buildDangerCard(BuildContext context, Widget child) {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.only(bottom: 8.0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.red.withAlpha(20),
            blurRadius: 6,
            spreadRadius: 0,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border.all(
          color: Colors.red.shade100,
          width: 1,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: child,
      ),
    );
  }

  // Information row with icon
  Widget _buildInfoRow(
      BuildContext context, String label, String value, IconData icon,
      {bool isHighlighted = false}) {
    return Row(
      children: [
        Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: isHighlighted
                ? const Color.fromARGB(255, 44, 148, 44).withAlpha(30)
                : Colors.grey.shade100,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            color: isHighlighted
                ? const Color.fromARGB(255, 44, 148, 44)
                : Colors.grey.shade700,
            size: 20,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Colors.grey.shade700,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                value,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight:
                      isHighlighted ? FontWeight.w600 : FontWeight.normal,
                  color: isHighlighted
                      ? const Color.fromARGB(255, 44, 148, 44)
                      : Colors.black87,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  // Method to handle image picking with permission handling
  Future<void> _pickAndUploadImage(BuildContext context) async {
    try {
      // Try to check photo library permission
      PermissionStatus photoStatus;
      try {
        photoStatus = await Permission.photos.status;

        if (photoStatus.isDenied) {
          // Request permission
          photoStatus = await Permission.photos.request();
        }

        if (photoStatus.isPermanentlyDenied) {
          // Show dialog to open settings
          if (context.mounted) {
            _showPermissionDeniedDialog(context, 'photo library');
          }
          return;
        }

        if (!photoStatus.isGranted) {
          // If permission is not granted, exit early
          return;
        }
      } catch (permissionError) {
        // If permission_handler fails, continue with image picker
        // We'll proceed with image picker directly, which will handle permissions on its own
      }

      // Proceed with image picking
      final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
      );

      if (image != null) {
        // Show progress indicator
        if (context.mounted) {
          showDialog(
            context: context,
            barrierDismissible: false,
            builder: (BuildContext context) {
              return Center(
                child: Container(
                  padding: const EdgeInsets.all(24),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const CircularProgressIndicator(
                        color: Color.fromARGB(255, 44, 148, 44),
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'Uploading thumbnail...',
                        style: TextStyle(
                          color: Colors.grey.shade800,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              );
            },
          );
        }

        try {
          String selectedFileName = image.name;
          ref.read(currentProjectThumbnailName.notifier).state =
              selectedFileName;

          String? imageUrl = await uploadData
              .uploadProjectThumbnailAndGetDownloadUrl(image, ref);

          if (imageUrl != null) {
            await logic.updateProjectThumbnail(
                ref, imageUrl, ref.read(currentProjectThumbnailName));

            // Update the thumbnail URL provider
            ref.read(currentProjectThumbnailUrlProvider.notifier).state =
                imageUrl;
          }

          // Close progress indicator
          if (context.mounted) {
            Navigator.of(context).pop();
          }

          // Show success message
          if (context.mounted && imageUrl != null) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Thumbnail added successfully.'),
                backgroundColor: Color.fromARGB(255, 44, 148, 44),
              ),
            );
          } else if (context.mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Failed to upload thumbnail.'),
                backgroundColor: Colors.red,
              ),
            );
          }
        } catch (uploadError) {
          // Close progress indicator
          if (context.mounted) {
            Navigator.of(context).pop();
          }

          // Show error message
          if (context.mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Error uploading thumbnail: $uploadError'),
                backgroundColor: Colors.red,
              ),
            );
          }
        }
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error picking image: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // Method to handle camera image picking with permission handling
  Future<void> _pickAndUploadCameraImage(BuildContext context) async {
    try {
      // Try to check camera permission
      PermissionStatus cameraStatus;
      try {
        cameraStatus = await Permission.camera.status;

        if (cameraStatus.isDenied) {
          // Request permission
          cameraStatus = await Permission.camera.request();
        }

        if (cameraStatus.isPermanentlyDenied) {
          // Show dialog to open settings
          if (context.mounted) {
            _showPermissionDeniedDialog(context, 'camera');
          }
          return;
        }

        if (!cameraStatus.isGranted) {
          // If permission is not granted, exit early
          return;
        }
      } catch (permissionError) {
        // If permission_handler fails, log the error and continue with image picker
        // We'll proceed with image picker directly, which will handle permissions on its own
      }

      // Proceed with camera
      final XFile? image = await _picker.pickImage(
        source: ImageSource.camera,
      );

      if (image != null) {
        // Show progress indicator
        if (context.mounted) {
          showDialog(
            context: context,
            barrierDismissible: false,
            builder: (BuildContext context) {
              return Center(
                child: Container(
                  padding: const EdgeInsets.all(24),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const CircularProgressIndicator(
                        color: Color.fromARGB(255, 44, 148, 44),
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'Uploading thumbnail...',
                        style: TextStyle(
                          color: Colors.grey.shade800,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              );
            },
          );
        }

        try {
          String selectedFileName = image.name;
          ref.read(currentProjectThumbnailName.notifier).state =
              selectedFileName;

          String? imageUrl = await uploadData
              .uploadProjectThumbnailAndGetDownloadUrl(image, ref);

          if (imageUrl != null) {
            await logic.updateProjectThumbnail(
                ref, imageUrl, ref.read(currentProjectThumbnailName));

            // Update the thumbnail URL provider
            ref.read(currentProjectThumbnailUrlProvider.notifier).state =
                imageUrl;
          }

          // Close progress indicator
          if (context.mounted) {
            Navigator.of(context).pop();
          }

          // Show success message
          if (context.mounted && imageUrl != null) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Thumbnail added successfully.'),
                backgroundColor: Color.fromARGB(255, 44, 148, 44),
              ),
            );
          } else if (context.mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Failed to upload thumbnail.'),
                backgroundColor: Colors.red,
              ),
            );
          }
        } catch (uploadError) {
          // Close progress indicator
          if (context.mounted) {
            Navigator.of(context).pop();
          }

          // Show error message
          if (context.mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Error uploading thumbnail: $uploadError'),
                backgroundColor: Colors.red,
              ),
            );
          }
        }
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error taking photo: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // Show dialog when permission is permanently denied
  void _showPermissionDeniedDialog(
      BuildContext context, String permissionType) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('$permissionType Access Required'),
          content: Text(
            'BookBranch needs access to your $permissionType to upload thumbnails. '
            'Please enable it in your device settings.',
          ),
          actions: [
            TextButton(
              child: const Text('Cancel'),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
            TextButton(
              child: const Text('Open Settings'),
              onPressed: () {
                Navigator.of(context).pop();
                openAppSettings();
              },
            ),
          ],
        );
      },
    );
  }

  void showWarningDialog(BuildContext context, WidgetRef ref) {
    bool isChecked = false;
    final scaffoldMessenger = ScaffoldMessenger.of(context);

    showDialog(
      context: context,
      builder: (BuildContext dialogContext) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: Row(
                children: [
                  Icon(Icons.warning_amber_rounded, color: Colors.red.shade700),
                  const SizedBox(width: 8),
                  const Text(
                    'Delete Project',
                    style: TextStyle(
                      color: Colors.black87,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              backgroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              content: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.red.shade50,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.red.shade200),
                      ),
                      child: const Text(
                        'This action cannot be undone. All project data, including files, discussions, and subscriber information will be permanently deleted.',
                        style: TextStyle(
                          color: Colors.black87,
                          fontSize: 14,
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        Checkbox(
                          value: isChecked,
                          onChanged: (bool? value) {
                            setState(() {
                              isChecked = value!;
                            });
                          },
                          activeColor: Colors.red.shade700,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(4),
                          ),
                        ),
                        Expanded(
                          child: Text(
                            'I understand this action cannot be undone',
                            style: TextStyle(
                              color: Colors.grey.shade800,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              actions: <Widget>[
                TextButton(
                  child: Text(
                    'Cancel',
                    style: TextStyle(
                      color: Colors.grey.shade700,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  onPressed: () {
                    Navigator.of(dialogContext).pop();
                  },
                ),
                ElevatedButton(
                  onPressed: isChecked
                      ? () async {
                          Navigator.of(dialogContext).pop(); // Close the dialog

                          // Show progress indicator
                          showDialog(
                            context: context,
                            barrierDismissible: false,
                            builder: (BuildContext context) {
                              return Center(
                                child: Container(
                                  padding: const EdgeInsets.all(24),
                                  decoration: BoxDecoration(
                                    color: Colors.white,
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: Column(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      const CircularProgressIndicator(
                                        color: Color.fromARGB(255, 44, 148, 44),
                                      ),
                                      const SizedBox(height: 16),
                                      Text(
                                        'Deleting project...',
                                        style: TextStyle(
                                          color: Colors.grey.shade800,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              );
                            },
                          );

                          try {
                            print('Starting project deletion for ID: ${ref.watch(currentProjectID)}');

                            FirebaseFunctions functions =
                                FirebaseFunctions.instance;
                            HttpsCallable callable =
                                functions.httpsCallable('deleteProject');

                            print('Calling cloud function...');
                            final result = await callable.call(
                                {'projectID': ref.watch(currentProjectID)});

                            print('Cloud function completed successfully: ${result.data}');

                            ref.invalidate(projectCountProvider);
                            // Use the result of refresh
                            final _ = ref.refresh(projectCountProvider);

                            print('About to navigate back to projects screen');
                            if (context.mounted) {
                              Navigator.of(context)
                                  .pop(); // Close the progress indicator
                              // Navigate back to projects screen by removing all routes and pushing projects screen
                              Navigator.of(context).pushNamedAndRemoveUntil(
                                  '/projectsScreen', (route) => false);
                            }
                            print('Navigation completed');
                          } catch (e) {
                            print('Error during project deletion: $e');
                            if (context.mounted) {
                              Navigator.of(context)
                                  .pop(); // Close the progress indicator
                              scaffoldMessenger.showSnackBar(SnackBar(
                                content: Text('Error: $e'),
                                backgroundColor: Colors.red,
                              ));
                            }
                          }
                        }
                      : null,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red.shade700,
                    foregroundColor: Colors.white,
                    elevation: 0,
                    disabledBackgroundColor: Colors.red.shade200,
                    disabledForegroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: const Text('Delete Project'),
                ),
              ],
            );
          },
        );
      },
    );
  }
}
